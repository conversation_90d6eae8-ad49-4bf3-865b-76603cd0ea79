#!/bin/bash

# 清理makeCreateResume组件的调试日志

echo "开始清理组件调试日志..."

files=(
    "pages/makeCreateResume/components/resumePreview/index.js"
    "pages/makeCreateResume/components/templateSelector/index.js"
    "pages/makeCreateResume/components/toolBar/index.js"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "处理文件: $file"
        
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 清理常见的调试日志
        sed -i '/console\.log.*组件/d' "$file"
        sed -i '/console\.log.*初始化/d' "$file"
        sed -i '/console\.log.*加载/d' "$file"
        sed -i '/console\.log.*更新/d' "$file"
        sed -i '/console\.log.*生成/d' "$file"
        sed -i '/console\.log.*完成/d' "$file"
        sed -i '/console\.log.*开始/d' "$file"
        sed -i '/console\.log.*成功/d' "$file"
        sed -i '/console\.log.*数据/d' "$file"
        sed -i '/console\.log.*配置/d' "$file"
        sed -i '/console\.log.*模板/d' "$file"
        sed -i '/console\.log.*预览/d' "$file"
        
        echo "✅ 完成: $file"
    else
        echo "❌ 文件不存在: $file"
    fi
done

echo "组件日志清理完成！"
