#!/bin/bash

# 专门清理makeCreateResume.js的脚本

file="pages/makeCreateResume/makeCreateResume.js"
echo "开始清理 $file 的调试日志..."

if [ -f "$file" ]; then
    # 备份原文件
    cp "$file" "$file.backup"
    
    # 清理各种调试日志模式
    sed -i '/console\.log.*页面加载完成/d' "$file"
    sed -i '/console\.log.*页面渲染完成/d' "$file"
    sed -i '/console\.log.*开始初始化/d' "$file"
    sed -i '/console\.log.*预览组件已找到/d' "$file"
    sed -i '/console\.log.*当前模板/d' "$file"
    sed -i '/console\.log.*当前配置/d' "$file"
    sed -i '/console\.log.*已触发.*预览生成/d' "$file"
    sed -i '/console\.log.*模板.*未发生变化/d' "$file"
    sed -i '/console\.log.*模板更新完成/d' "$file"
    sed -i '/console\.log.*主题色更新为/d' "$file"
    sed -i '/console\.log.*配置更新:/d' "$file"
    sed -i '/console\.log.*开始生成PDF/d' "$file"
    sed -i '/console\.log.*PDF生成响应/d' "$file"
    sed -i '/console\.log.*响应数据详情/d' "$file"
    sed -i '/console\.log.*获取到PDF URL/d' "$file"
    sed -i '/console\.log.*准备下载PDF/d' "$file"
    sed -i '/console\.log.*PDF下载成功/d' "$file"
    sed -i '/console\.log.*PDF打开成功/d' "$file"
    sed -i '/console\.log.*发现.*个PDF文件/d' "$file"
    sed -i '/console\.log.*PDF文件总大小/d' "$file"
    sed -i '/console\.log.*准备删除.*个旧PDF文件/d' "$file"
    sed -i '/console\.log.*删除旧PDF文件成功/d' "$file"
    sed -i '/console\.log.*无需删除PDF文件/d' "$file"
    sed -i '/console\.log.*数据大小:/d' "$file"
    sed -i '/console\.log.*简历数据未发生变化/d' "$file"
    sed -i '/console\.log.*优化后数据设置完成/d' "$file"
    sed -i '/console\.log.*当前页面数据大小/d' "$file"
    sed -i '/console\.log.*头像数据大小/d' "$file"
    sed -i '/console\.log.*数据设置完成.*准备触发/d' "$file"
    sed -i '/console\.log.*已触发数据设置后的预览生成/d' "$file"
    sed -i '/console\.log.*配置.*未发生变化/d' "$file"
    sed -i '/console\.log.*配置.*更新为/d' "$file"
    sed -i '/console\.log.*最终性能报告/d' "$file"
    sed -i '/console\.log.*数据传输趋势/d' "$file"
    sed -i '/console\.log.*页面已卸载/d' "$file"
    sed -i '/console\.log.*PDF生成请求超时/d' "$file"
    
    # 清理注释掉的console语句
    sed -i '/\/\/.*console\./d' "$file"
    
    # 清理一些警告日志（保留错误日志）
    sed -i '/console\.warn.*没有接收到简历数据/d' "$file"
    sed -i '/console\.warn.*没有简历数据.*无法生成预览/d' "$file"
    sed -i '/console\.warn.*预览组件未找到/d' "$file"
    sed -i '/console\.warn.*数据大小超过限制/d' "$file"
    sed -i '/console\.warn.*头像数据过大/d' "$file"
    sed -i '/console\.warn.*读取用户数据目录失败/d' "$file"
    sed -i '/console\.warn.*清理PDF文件时出错/d' "$file"
    sed -i '/console\.warn.*删除PDF文件失败/d' "$file"
    
    echo "✅ 完成清理 $file"
else
    echo "❌ 文件不存在: $file"
fi

echo "makeCreateResume.js 清理完成！"
