// 引入API模块
const resumeApi = require('../../utils/api/resumeApi');
// 引入性能监控
const performanceMonitor = require('../../utils/performance/performanceMonitor');
// 引入存储管理器
const storageManager = require('../../utils/storage/storageManager');
const app = getApp();

Page({
  data: {
    resumeData: null,    // 初始值设为 null
    // 为每个模板定义默认主题色
    templateConfigs: {
      templateA01: {
        themeColor: '#2B6CB0',  // 商务蓝
        name: '模板一'
      },
      templateA02: {
        themeColor: '#44546B',  // 灰蓝色
        name: '模板二'
      },
      templateA03: {
        themeColor: '#2E75B6',  // 偏蓝色
        name: '模板三'
      }
    },
    config: {
      themeColor: '#2B6CB0',
      fontSize: 11,
      spacing: 1.5,
      showCover: false
    },
    template: {
      id: 'templateA01',  // 修改为与模板组件匹配的ID
      name: '模板一',
      thumbnail: '/assets/images/template1.png'
    },

    // 性能优化相关
    lastResumeDataHash: '', // 上次简历数据的哈希值
    updateTimer: null, // 更新防抖定时器
    performanceConfig: {
      updateDelay: 50, // 更新延迟时间
      maxDataSize: 1024 * 1024, // 最大数据传输大小 1MB
      enableDataCompression: true // 启用数据压缩
    }
  },

  onLoad(options) {


    try {
      // 初始化云环境
      // if (!wx.cloud) {
      //   console.error('请使用 2.2.3 或以上的基础库以使用云能力');
      //   return;
      // }
      // wx.cloud.init({
      //   env: 'jianli-6ggquwcuaee294d1', // 使用简短的环境ID
      //   traceUser: true
      // });


      // // 检查组件是否存在
      // const preview = this.selectComponent('resumePreview');
      // const toolbar = this.selectComponent('toolBar');
      // const templateSelector = this.selectComponent('templateSelector');

      // console.log('组件加载状态：', {
      //   'resumePreview': !!preview,
      //   'toolBar': !!toolbar,
      //   'templateSelector': !!templateSelector
      // });

      // if (!preview || !toolbar || !templateSelector) {
      //   console.error('组件加载失败');
      //   wx.showToast({
      //     title: '页面加载异常',
      //     icon: 'none'
      //   });
      //   return;
      // }

      // 解析传递过来的数据
      if (options.resumeData) {
        try {
          const resumeData = JSON.parse(decodeURIComponent(options.resumeData));




          // 打印格式化后的数据


          // 使用优化后的数据设置方法
          this.setResumeDataOptimized(resumeData);


        } catch (error) {
          console.error('数据解析失败：', error);
          wx.showToast({
            title: '数据加载失败',
            icon: 'none'
          });
        }
      } else {
        console.warn('没有接收到简历数据');
      }


    } catch (error) {
      console.error('页面加载错误：', error);

      // 上报页面加载失败错误
      const app = getApp();
      app.reportError('page_load_error', error, {
        page: 'makeCreateResume',
        action: 'onLoad',
        step: 'page_initialization',
        options: options
      });

      wx.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
    }

    // console.log(options.resumeData);
  },

  // 页面初次渲染完成
  onReady() {

    try {
      // 确保页面渲染完成后再触发预览生成
      wx.nextTick(() => {
        this.initializeDefaultPreview();
      });
    } catch (error) {
      console.error('页面渲染完成处理失败:', error);

      // 上报页面渲染失败错误
      const app = getApp();
      app.reportError('page_ready_error', error, {
        page: 'makeCreateResume',
        action: 'onReady',
        step: 'initialize_preview'
      });
    }
  },

  // 初始化默认预览
  initializeDefaultPreview() {

    // 检查是否有简历数据
    if (!this.data.resumeData) {
      console.warn('没有简历数据，无法生成预览');
      return;
    }

    // 获取预览组件
    const preview = this.selectComponent('#resume-preview');
    if (!preview) {
      console.error('未找到预览组件，延迟重试...');
      // 延迟重试
      setTimeout(() => {
        this.initializeDefaultPreview();
      }, 500);
      return;
    }



    // 确保预览组件有正确的配置
    preview.updateStyle(this.data.config);

    // 延迟一点时间确保组件完全初始化
    setTimeout(() => {
      // 触发预览图片生成
      preview.debounceRequestPreviewImage();
    }, 100);
  },

  // 处理模板选择
  handleTemplateSelect(e) {
    const template = e.detail;

    // 检查是否选择了相同的模板
    if (this.data.template.id === template.id) {
      console.log(`模板 ${template.id} 未发生变化，跳过更新预览`);
      return; // 相同模板，直接返回，不更新预览
    }

    const templateConfig = this.data.templateConfigs[template.id];

    if (templateConfig) {
      // 先设置模板和主题色
      this.setData({
        template,
        'config.themeColor': templateConfig.themeColor
      }, () => {
        // 获取预览组件
        const preview = this.selectComponent('#resume-preview');
        if (preview) {
          // 更新样式配置（但不触发预览请求）
          preview.updateStyle(this.data.config);

          // 确保数据都已设置完毕后，通过延时执行一次性请求预览图片
          setTimeout(() => {
            // 请求服务端生成预览图片（使用自带防抖）
            preview.debounceRequestPreviewImage();

            // 记录用户行为
            // if (app && app.trackUserAction) {
            //   app.trackUserAction('template_switch', {
            //     templateId: template.id,
            //     templateName: templateConfig.name
            //   });
            // }

            console.log('模板更新完成：', this.data.template);
            console.log('主题色更新为：', templateConfig.themeColor);
          }, 1);
        } else {
          console.error('未找到预览组件');
        }
      });
    }
  },

  // 处理配置变更（优化版本）
  handleConfigChange(e) {
    const field = e.detail.field;
    const value = e.detail.value;

    // 使用优化后的配置更新方法
    this.updateConfigOptimized(field, value);

    // 配置变更时不显示全局loading，由预览组件内部的loading状态处理
    console.log(`配置更新: ${field} = ${value}`);
  },

  // 处理PDF生成
  async handleGeneratePDF() {
    const app = getApp();

    try {
      wx.showLoading({
        title: '正在生成PDF...',
        mask: true
      });

      console.log('开始生成PDF...');
      console.log('当前配置:', this.data.config);
      console.log('简历数据:', this.data.resumeData);
      console.log('当前模板:', this.data.template);

      // 使用API模块生成PDF（新版本 - 返回URL）
      const response = await resumeApi.generatePDF(
        this.data.resumeData,
        this.data.config,
        this.data.template.id
      );

      console.log('PDF生成响应:', response);
      console.log('响应数据详情:', JSON.stringify(response, null, 2));

      if (response.success && response.data && response.data.pdf_url) {
        console.log('获取到PDF URL:', response.data.pdf_url);

        // 获取简历名称，用于文件命名
        const resumeName = wx.getStorageSync('resumeName') || 'resume';
        const safeResumeName = resumeName.replace(/[\\/:*?"<>|]/g, '_'); // 移除不安全字符
        // const todayDate = new Date().toISOString().split('T')[0];
        const fileName = `${safeResumeName}.pdf`;

        console.log('准备下载PDF文件，文件名:', fileName);

        // 清理旧的PDF文件
        await storageManager.cleanupFilesByType('pdf');

        // 直接下载到用户数据目录，指定文件名
        const finalFilePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

        await new Promise((resolve, reject) => {
          wx.downloadFile({
            url: response.data.pdf_url,
            filePath: finalFilePath,
            success: (res) => {
              console.log('PDF下载成功，文件路径:', res.filePath);

              if (res.statusCode === 200) {
                // 打开PDF文件
                wx.openDocument({
                  filePath: res.filePath,
                  fileType: 'pdf',
                  showMenu: true,
                  success: () => {
                    console.log('PDF打开成功');
                    resolve();
                  },
                  fail: (error) => {
                    console.error('PDF打开失败:', error);
                    wx.showToast({
                      title: '文件打开失败，请重试',
                      icon: 'none'
                    });
                    reject(error);
                  }
                });
              } else {
                reject(new Error(`下载失败，状态码: ${res.statusCode}`));
              }
            },
            fail: (error) => {
              console.error('PDF下载失败:', error);
              reject(error);
            }
          });
        });

      } else {
        console.error('服务器返回数据格式错误:', response);
        throw new Error('服务器返回数据格式错误');
      }



      wx.hideLoading();
      wx.showToast({
        title: 'PDF生成成功',
        icon: 'success',
        duration: 2000
      });

    } catch (error) {
      wx.hideLoading();
      console.error('生成PDF过程中发生错误:', error);

      // 上报PDF生成失败错误
      app.reportError('pdf_generate_error', error, {
        page: 'makeCreateResume',
        action: 'handleGeneratePDF',
        template_id: this.data.template?.id,
        is_timeout: error.isTimeout,
        error_message: error.message
      });

      // 检查是否为超时错误
      let errorMessage = error.message || '生成PDF失败';
      if (error.isTimeout) {
        errorMessage = 'PDF生成超时，请重试';
        console.log('PDF生成请求超时，已自动释放加载状态');
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      });

      throw error;
    }
  },

  // 处理重命名事件
  handleRename(e) {
    const name = e.detail.name;
    // 将新名称保存到本地存储
    wx.setStorageSync('resumeName', name);
  },

  // 清理旧的PDF文件
  cleanupOldPDFFiles() {
    return new Promise((resolve) => {
      try {
        const fs = wx.getFileSystemManager();
        const userDataPath = wx.env.USER_DATA_PATH;

        // 读取用户数据目录
        fs.readdir({
          dirPath: userDataPath,
          success: (res) => {
            // 查找所有PDF文件
            const pdfFiles = res.files.filter(file =>
              file.endsWith('.pdf')
            );

            console.log(`发现 ${pdfFiles.length} 个PDF文件`);

            if (pdfFiles.length === 0) {
              resolve();
              return;
            }

            // 获取文件详细信息并按时间排序
            const filePromises = pdfFiles.map(file => {
              return new Promise((fileResolve) => {
                const filePath = `${userDataPath}/${file}`;
                fs.stat({
                  path: filePath,
                  success: (stat) => {
                    fileResolve({
                      name: file,
                      path: filePath,
                      size: stat.stats.size,
                      mtime: stat.stats.mtime
                    });
                  },
                  fail: () => {
                    fileResolve(null);
                  }
                });
              });
            });

            Promise.all(filePromises).then(fileInfos => {
              // 过滤掉无效文件信息
              const validFiles = fileInfos.filter(info => info !== null);

              // 按修改时间排序（最新的在前）
              validFiles.sort((a, b) => b.mtime - a.mtime);

              // 计算总文件大小
              const totalSize = validFiles.reduce((sum, file) => sum + file.size, 0);
              console.log(`PDF文件总大小: ${this.formatFileSize(totalSize)}`);

              // 清理策略：保留最新的3个文件，或总大小不超过20MB
              const maxFiles = 3;
              const maxTotalSize = 20 * 1024 * 1024; // 20MB

              let filesToDelete = [];
              let currentSize = 0;
              let keepCount = 0;

              for (let i = 0; i < validFiles.length; i++) {
                const file = validFiles[i];

                if (keepCount < maxFiles && currentSize + file.size <= maxTotalSize) {
                  // 保留这个文件
                  currentSize += file.size;
                  keepCount++;
                } else {
                  // 删除这个文件
                  filesToDelete.push(file);
                }
              }

              // 执行删除操作
              if (filesToDelete.length > 0) {
                console.log(`准备删除 ${filesToDelete.length} 个旧PDF文件`);
                let deleteCount = 0;

                filesToDelete.forEach(file => {
                  fs.unlink({
                    filePath: file.path,
                    success: () => {
                      console.log(`删除旧PDF文件成功: ${file.name} (${this.formatFileSize(file.size)})`);
                      deleteCount++;
                      if (deleteCount === filesToDelete.length) {
                        resolve();
                      }
                    },
                    fail: (error) => {
                      console.warn(`删除PDF文件失败: ${file.name}`, error);
                      deleteCount++;
                      if (deleteCount === filesToDelete.length) {
                        resolve();
                      }
                    }
                  });
                });
              } else {
                console.log('无需删除PDF文件');
                resolve();
              }
            });
          },
          fail: (error) => {
            console.warn('读取用户数据目录失败:', error);
            resolve();
          }
        });
      } catch (error) {
        console.warn('清理PDF文件时出错:', error);
        resolve();
      }
    });
  },

  // 格式化文件大小显示
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // ========== 性能优化方法 ==========

  // 生成简历数据哈希值
  generateResumeDataHash(resumeData) {
    const keyData = {
      basicInfo: resumeData.basicInfo || {},
      jobIntention: resumeData.jobIntention || {},
      education: resumeData.education || [],
      work: resumeData.work || [],
      project: resumeData.project || [],
      skills: resumeData.skills || [],
      moduleOrders: resumeData.moduleOrders || {}
    };

    const dataString = JSON.stringify(keyData);
    let hash = 0;
    for (let i = 0; i < dataString.length; i++) {
      const char = dataString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString();
  },

  // 检查数据大小
  checkDataSize(data) {
    const dataString = JSON.stringify(data);
    const sizeInBytes = this.getStringByteLength(dataString);
    const sizeInKB = Math.round(sizeInBytes / 1024);

    console.log(`数据大小: ${sizeInKB} KB`);

    if (sizeInBytes > this.data.performanceConfig.maxDataSize) {
      console.warn(`数据大小超过限制: ${sizeInKB} KB > ${this.data.performanceConfig.maxDataSize / 1024} KB`);
      return false;
    }

    return true;
  },

  // 获取字符串字节长度（兼容微信小程序）
  getStringByteLength(str) {
    let byteLength = 0;
    for (let i = 0; i < str.length; i++) {
      const charCode = str.charCodeAt(i);
      if (charCode <= 0x7F) {
        byteLength += 1;
      } else if (charCode <= 0x7FF) {
        byteLength += 2;
      } else if (charCode <= 0xFFFF) {
        byteLength += 3;
      } else {
        byteLength += 4;
      }
    }
    return byteLength;
  },

  // 优化后的简历数据设置方法
  setResumeDataOptimized(resumeData) {
    // 检查数据大小
    if (!this.checkDataSize(resumeData)) {
      wx.showToast({
        title: '数据过大，请简化内容',
        icon: 'none'
      });
      return;
    }

    // 生成数据哈希值
    const currentHash = this.generateResumeDataHash(resumeData);

    // 检查数据是否发生变化
    if (currentHash === this.data.lastResumeDataHash) {
      console.log('简历数据未发生变化，跳过更新');
      return;
    }

    // 清除之前的定时器
    if (this.data.updateTimer) {
      clearTimeout(this.data.updateTimer);
    }

    // 使用防抖方式更新数据，并分批传输
    const timer = setTimeout(() => {
      wx.nextTick(() => {
        // 分离头像数据，减少单次传输量
        const basicInfo = resumeData.basicInfo || {};
        const photoUrl = basicInfo.photoUrl;
        const basicInfoWithoutPhoto = Object.assign({}, basicInfo);
        delete basicInfoWithoutPhoto.photoUrl;

        const otherData = Object.assign({}, resumeData);
        delete otherData.basicInfo;

        // 第一批：更新不包含头像的数据
        const updateData = {
          resumeData: Object.assign({}, otherData, {
            basicInfo: basicInfoWithoutPhoto
          }),
          lastResumeDataHash: currentHash
        };

        // 记录性能数据
        performanceMonitor.recordSetData(updateData, 'makeCreateResume-main');

        this.setData(updateData, () => {
          console.log('优化后数据设置完成，数据哈希:', currentHash);
          console.log('当前页面数据大小:', JSON.stringify(this.data.resumeData).length);

          // 第二批：如果有头像，检查并优化后再更新
          if (photoUrl) {
            setTimeout(() => {
              // 检查图片大小，如果过大则进行额外优化
              const photoSize = this.getBase64Size(photoUrl);
              console.log('头像数据大小:', this.formatSize(photoSize));

              if (photoSize > 300 * 1024) { // 超过300KB
                console.warn('头像数据过大，建议重新压缩');
                // 可以在这里添加重新压缩的逻辑
              }

              const photoData = { 'resumeData.basicInfo.photoUrl': photoUrl };
              performanceMonitor.recordSetData(photoData, 'makeCreateResume-photo');
              this.setData(photoData, () => {
                // 头像设置完成后，触发预览生成
                this.triggerPreviewAfterDataSet();
              });
            }, 100);
          } else {
            // 没有头像时，直接触发预览生成
            this.triggerPreviewAfterDataSet();
          }
        });
      });
    }, this.data.performanceConfig.updateDelay);

    this.setData({
      updateTimer: timer
    });
  },

  // 数据设置完成后触发预览生成
  triggerPreviewAfterDataSet() {
    console.log('数据设置完成，准备触发预览生成...');

    // 获取预览组件
    const preview = this.selectComponent('#resume-preview');
    if (!preview) {
      console.warn('预览组件未找到，跳过预览生成');
      return;
    }

    // 确保预览组件有正确的配置
    preview.updateStyle(this.data.config);

    // 延迟一点时间确保数据完全设置完成
    setTimeout(() => {
      // 触发预览图片生成
      preview.debounceRequestPreviewImage();
      console.log('已触发数据设置后的预览生成');
    }, 200);
  },

  // 优化后的配置更新方法
  updateConfigOptimized(field, value) {
    // 检查是否真的发生了变化
    if (this.data.config[field] === value) {
      console.log(`配置 ${field} 未发生变化，跳过更新`);
      return;
    }

    // 清除之前的定时器
    if (this.data.updateTimer) {
      clearTimeout(this.data.updateTimer);
    }

    // 使用防抖方式更新配置
    const timer = setTimeout(() => {
      this.setData({
        [`config.${field}`]: value
      }, () => {
        console.log(`配置 ${field} 更新为:`, value);

        // 获取预览组件并更新
        const preview = this.selectComponent('#resume-preview');
        if (preview) {
          preview.updateStyle(this.data.config);

          // 只有影响渲染的配置才触发预览更新
          if (['fontSize', 'spacing', 'themeColor'].includes(field)) {
            preview.debounceRequestPreviewImage();
          }
        }
      });
    }, this.data.performanceConfig.updateDelay);

    this.setData({
      updateTimer: timer
    });
  },

  // 计算base64图片大小
  getBase64Size(base64) {
    if (!base64) return 0;
    // 移除data:image/...;base64,前缀
    const base64Data = base64.split(',')[1] || base64;
    // base64每4个字符代表3个字节
    return Math.floor(base64Data.length * 3 / 4);
  },

  // 格式化文件大小显示
  formatSize(bytes) {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return Math.round(bytes / 1024) + ' KB';
    return Math.round(bytes / (1024 * 1024)) + ' MB';
  },

  // 页面卸载时清理定时器
  onUnload() {
    if (this.data.updateTimer) {
      clearTimeout(this.data.updateTimer);
    }

    // 隐藏可能存在的PDF生成loading（保留这个，因为PDF生成仍使用全局loading）
    wx.hideLoading();

    // 打印最终性能报告
    console.log('========== makeCreateResume 最终性能报告 ==========');
    performanceMonitor.printReport();
    console.log('数据传输趋势:', performanceMonitor.getDataTransferTrend());
    console.log('===============================================');

    console.log('makeCreateResume页面已卸载，定时器已清理');
  }
});