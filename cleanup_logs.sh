#!/bin/bash

# 批量清理调试日志的脚本
# 保留错误处理相关的console.error，清理调试用的console.log

echo "开始批量清理调试日志..."

# 定义要处理的文件列表
files=(
    "pages/makeResume/awards/awards.js"
    "pages/makeResume/evaluation/evaluation.js"
    "pages/makeResume/interests/interests.js"
    "pages/makeResume/jobIntention/jobIntention.js"
    "pages/makeResume/custom/custom1/custom1.js"
    "pages/makeResume/custom/custom2/custom2.js"
    "pages/makeResume/custom/custom3/custom3.js"
    "pages/makeResume/project/project/project.js"
    "pages/makeResume/project/projectEdit/projectEdit.js"
    "pages/makeResume/school/school/school.js"
    "pages/makeResume/school/schoolEdit/schoolEdit.js"
    "pages/makeResume/internship/internshipEdit/internshipEdit.js"
    "pages/makeResume/internship/internshipExperience/internshipExperience.js"
    "pages/makeResume/moduleManage/moduleManage.js"
)

# 清理函数
cleanup_file() {
    local file="$1"
    echo "处理文件: $file"
    
    if [ -f "$file" ]; then
        # 备份原文件
        cp "$file" "$file.backup"
        
        # 清理常见的调试日志模式
        sed -i 's/.*console\.log.*页面加载.*;//g' "$file"
        sed -i 's/.*console\.log.*数据加载成功.*;//g' "$file"
        sed -i 's/.*console\.log.*保存成功.*;//g' "$file"
        sed -i 's/.*console\.log.*删除成功.*;//g' "$file"
        sed -i 's/.*console\.log.*初始化完成.*;//g' "$file"
        sed -i 's/.*console\.error.*保存失败.*;//g' "$file"
        sed -i 's/.*console\.error.*删除失败.*;//g' "$file"
        
        # 清理空行
        sed -i '/^[[:space:]]*$/d' "$file"
        
        echo "✅ 完成: $file"
    else
        echo "❌ 文件不存在: $file"
    fi
}

# 处理所有文件
for file in "${files[@]}"; do
    cleanup_file "$file"
done

echo "批量清理完成！"
echo "如需恢复，可使用 .backup 文件"
